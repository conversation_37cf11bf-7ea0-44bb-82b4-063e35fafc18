import time
import uuid
from typing import Any, cast

from pydantic import BaseModel
import pydantic_core
from fastmcp.server.middleware import <PERSON>Next, Middleware, MiddlewareContext
from loguru import logger

from action_server.core.config import Config


def default_serializer(data: Any) -> str:
    """Serialize data to JSON string."""
    return pydantic_core.to_json(data, fallback=str).decode()


class RequestDetails(BaseModel):
    method: str
    source: str
    message_type: str
    message_data: dict[str, Any] | str


class RequestContextData(BaseModel):
    request_id: uuid.UUID
    session_id: uuid.UUID
    action_record_id: uuid.UUID | None = None
    agent_identity: str
    start_time: float = time.perf_counter()
    timestamp: str
    tool_name: str
    tool_arguments: dict[str, Any]
    request_details: RequestDetails


class RequestLoggingMiddleware(Middleware):
    def __init__(self):
        self.max_payload_length = 1000

    def _truncate_payload(self, payload: str) -> str:
        """Truncate payload if it's too long."""
        if len(payload) > self.max_payload_length:
            return payload[: self.max_payload_length] + "..."
        return payload

    def _serialize_message(self, message: Any) -> dict[str, Any] | str:
        """Serialize message to a dictionary for logging."""
        try:
            serialized = default_serializer(message)
            if Config.IS_PROD_LIKE:
                return serialized
            return self._truncate_payload(serialized)
        except Exception as e:
            logger.warning(f"Failed to serialize message: {e}")
            return self._truncate_payload(str(message))

    def _create_context_data(self, context: MiddlewareContext) -> RequestContextData:
        """Get or create context data for request tracking."""

        request_id = uuid.uuid4()
        session_id = uuid.uuid4()
        agent_identity = context.source or "unknown"
        method = context.method or "unknown"
        tool_name = getattr(context.message, "name", "unknown")
        tool_arguments = getattr(context.message, "arguments", {})

        context_data = RequestContextData(
            request_id=request_id,
            session_id=session_id,
            agent_identity=agent_identity,
            start_time=time.perf_counter(),
            timestamp=context.timestamp.isoformat(),
            tool_name=tool_name,
            tool_arguments=tool_arguments,
            request_details=RequestDetails(
                method=method,
                source=context.source,
                message_type=context.type,
                message_data=self._serialize_message(context.message),
            ),
        )

        return context_data

    async def on_message(self, context: MiddlewareContext, call_next: CallNext):
        """Called for all MCP messages (requests and notifications)."""
        # Get or create context data (this will be the first hook called)
        context_data = self._create_context_data(context)
        # Store context data for use in other hooks and error handling
        try:
            setattr(context, "pmcp_context_data", context_data)
        except Exception as e:
            logger.exception(f"Failed to set context data: {e}")

        request_id = context_data.request_id
        session_id = context_data.session_id
        timestamp = context_data.timestamp
        agent_identity = context_data.agent_identity
        request_details = context_data.request_details

        with logger.contextualize(request_id=request_id, session_id=session_id):
            logger.info(
                "Processing message",
                **request_details.model_dump(),
                timestamp=timestamp,
                agent_identity=agent_identity,
            )

            result = await call_next(context)

            process_time_ms = round(
                (time.perf_counter() - context_data.start_time) * 1000, 3
            )
            response_details: dict[str, Any] = {
                **request_details.model_dump(),
                "agent_identity": agent_identity,
                "duration_ms": process_time_ms,
            }

            logger.info("Completed message", **response_details)
            return result

    # async def on_call_tool(self, context: MiddlewareContext, call_next: CallNext):
    #     """Called when tools are being executed."""
    #     # Get context data from previous hooks
    #     context_data = cast(RequestContextData, getattr(context, "pmcp_context_data"))

    #     session_id = context_data.session_id
    #     agent_identity = context_data.agent_identity
    #     tool_name = context_data.tool_name
    #     tool_arguments = context_data.tool_arguments

    #     # action_record = await ActionLoggerService.log_client_request(
    #     #     session_id=session_id,
    #     #     agent_identity=agent_identity,
    #     #     method=f"tools/call:{tool_name}",
    #     #     request_payload={
    #     #         "tool_name": tool_name,
    #     #         "arguments": tool_arguments,
    #     #     },
    #     #     status=RequestStatus.PENDING,
    #     # )
        
    #     result = await call_next(context)

    #     action_record_id = uuid.uuid4()

    #     context_data.action_record_id = action_record_id
        
    #     return result
