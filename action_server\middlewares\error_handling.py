import asyncio
import json
import time
from typing import Any, cast

import pydantic_core
from fastmcp.server.middleware import CallNext, Middleware, MiddlewareContext
from loguru import logger
from mcp import McpError
from mcp.types import ErrorData

from shared.exceptions import (
    ContentTooLargeError,
    InvalidRequestError,
    MethodNotFoundError,
    ParseError,
    RequestCancelledError,
    ResourceUnavailableError,
)

from .request_logging import RequestContextData


def default_serializer(data: Any) -> str:
    return pydantic_core.to_json(data, fallback=str).decode()


def map_error_to_mcp_error(error: Exception) -> Exception:
    """Transform non-MCP errors to proper MCP errors."""
    if isinstance(error, McpError):
        return error

    # Map common exceptions to appropriate MCP error codes
    error_type = type(error)

    if error_type in (
        ResourceUnavailableError,
        ConnectionError,
        TimeoutError,
        asyncio.TimeoutError,
    ):
        # Resource Unavailable (-32000): Temporary resource failure
        return McpError(
            ErrorData(code=-32000, message=f"Resource unavailable: {str(error)}")
        )
    elif error_type in (FileNotFoundError, KeyError):
        return McpError(
            ErrorData(code=-32001, message=f"Resource not found: {str(error)}")
        )
    elif error_type is PermissionError:
        return McpError(
            ErrorData(code=-32002, message=f"Permission denied: {str(error)}")
        )
    elif error_type is NotImplementedError:
        return McpError(
            ErrorData(code=-32003, message=f"Feature not implemented: {str(error)}")
        )
    elif error_type is InvalidRequestError:
        # Invalid Request (-32600): Missing required fields (jsonrpc, method, id)
        return McpError(
            ErrorData(
                code=-32600,
                message=f"Invalid request: Missing required fields - {str(error)}",
            )
        )
    elif error_type in (AttributeError, MethodNotFoundError):
        # Method Not Found (-32601): Unknown method called
        return McpError(
            ErrorData(code=-32601, message=f"Method not found: {str(error)}")
        )
    elif error_type in (ValueError, TypeError):
        # Invalid Params (-32602): Parameter validation failed
        return McpError(ErrorData(code=-32602, message=f"Invalid params: {str(error)}"))
    elif error_type in (RuntimeError, SystemError, MemoryError, OSError):
        # Internal Error (-32603): Server exception
        return McpError(
            ErrorData(
                code=-32603,
                message="Internal error occurred while processing your request",
            )
        )
    elif error_type in (json.JSONDecodeError, ParseError):
        # Parse Error (-32700): Invalid JSON received
        return McpError(
            ErrorData(
                code=-32700,
                message=f"Parse error: Invalid JSON received - {str(error)}",
            )
        )
    elif error_type in (asyncio.CancelledError, RequestCancelledError):
        # Request Cancelled (-32800): Client cancelled operation
        return McpError(
            ErrorData(code=-32800, message=f"Request cancelled: {str(error)}")
        )
    elif error_type in (ContentTooLargeError, OverflowError):
        # Content Too Large (-32801): Payload exceeds limits
        return McpError(
            ErrorData(code=-32801, message=f"Content too large: {str(error)}")
        )
    else:
        # Fallback for any other exceptions
        return McpError(
            ErrorData(
                code=-32603,
                message="Internal error occurred while processing your request",
            )
        )


class ErrorHandlingMiddleware(Middleware):
    async def on_message(self, context: MiddlewareContext, call_next: CallNext):
        try:
            return await call_next(context)
        except Exception as e:
            context_data = cast(
                RequestContextData, getattr(context, "pmcp_context_data")
            )
            start_time = context_data.start_time
            request_details = context_data.request_details.model_dump()
            process_time_ms = round((time.perf_counter() - start_time) * 1000, 3)

            error_type = type(e).__name__
            error_log_details = {
                **request_details,
                "duration_ms": process_time_ms,
                "session_id": str(context_data.session_id),
                "request_id": str(context_data.request_id),
                "error_type": error_type,
                "error_message": str(e),
            }

            # Include additional context if available
            agent_identity = getattr(context, "pmcp_agent_identity", None)

            if agent_identity:
                error_log_details["agent_identity"] = agent_identity
            if context_data.action_record_id:
                error_log_details["action_id"] = str(context_data.action_record_id)

            logger.exception(
                f"Error while processing a '{context.method}' request: {error_type}: {str(e)}",
                **error_log_details,
            )

            # Transform and re-raise
            transformed_error = map_error_to_mcp_error(e)
            raise transformed_error
