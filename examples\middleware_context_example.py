#!/usr/bin/env python3
"""
Example demonstrating how the PMCP middleware context passing works.

This example shows how request_details and context information flows
through the middleware hooks when a client makes a tool call.
"""

import asyncio
import uuid
from datetime import datetime
from typing import Any, Dict

from fastmcp.server.middleware import MiddlewareContext
from mcp.types import RequestMessage, CallToolRequest


class MockContext:
    """Mock MiddlewareContext for demonstration purposes."""
    
    def __init__(self, method: str = "call_tool", source: str = "test_agent"):
        self.method = method
        self.source = source
        self.type = "request"
        self.timestamp = datetime.now()
        self.message = CallToolRequest(
            method="call_tool",
            params={
                "name": "marketplace_get_quote",
                "arguments": {"item": "widget", "quantity": 10}
            }
        )


def demonstrate_context_flow():
    """Demonstrate how context flows through the middleware hooks."""
    
    print("=== PMCP Middleware Context Flow Demonstration ===\n")
    
    # Simulate incoming request
    context = MockContext()
    
    print("1. INCOMING REQUEST")
    print(f"   Method: {context.method}")
    print(f"   Source: {context.source}")
    print(f"   Message Type: {context.type}")
    print(f"   Timestamp: {context.timestamp.isoformat()}")
    print()
    
    # Simulate on_message hook (first hook called)
    print("2. ON_MESSAGE HOOK (ComprehensiveLoggingMiddleware)")
    
    # Create context data (this is what _get_or_create_context_data does)
    request_id = str(uuid.uuid4())
    session_id = uuid.uuid4()
    agent_identity = context.source or "unknown"
    method = context.method or "unknown"
    
    context_data = {
        "request_id": request_id,
        "session_id": session_id,
        "agent_identity": agent_identity,
        "method": method,
        "start_time": 1234567890.123,  # Mock time
        "request_details": {
            "method": method,
            "source": context.source,
            "message_type": context.type,
            "message_data": {
                "raw_message": str(context.message)[:100] + "...",
                "message_type": type(context.message).__name__,
            },
            "timestamp": context.timestamp.isoformat(),
        }
    }
    
    # Store context data (this is what setattr does)
    setattr(context, "pmcp_context_data", context_data)
    setattr(context, "pmcp_session_id", session_id)
    setattr(context, "pmcp_agent_identity", agent_identity)
    setattr(context, "pmcp_request_details", context_data["request_details"])
    
    print(f"   Created Request ID: {request_id}")
    print(f"   Created Session ID: {session_id}")
    print(f"   Agent Identity: {agent_identity}")
    print(f"   Request Details: {context_data['request_details']}")
    print()
    
    # Simulate on_request hook
    print("3. ON_REQUEST HOOK (ComprehensiveLoggingMiddleware)")
    
    # Mock ActionHistory record creation
    mock_action_id = uuid.uuid4()
    print(f"   Created ActionHistory record: {mock_action_id}")
    print(f"   Action Type: ClientRequest")
    print(f"   Actor: Client")
    print(f"   Status: PENDING")
    print(f"   Request Payload: {context_data['request_details']}")
    
    # Store additional context
    setattr(context, "pmcp_parent_action_id", mock_action_id)
    setattr(context, "pmcp_action_record", {"id": mock_action_id})
    print()
    
    # Simulate on_call_tool hook
    print("4. ON_CALL_TOOL HOOK (ComprehensiveLoggingMiddleware)")
    
    # Extract tool information
    tool_name = "marketplace_get_quote"
    tool_arguments = {"item": "widget", "quantity": 10}
    target_mcp = "marketplace_mcp"  # Determined by routing logic
    
    # Mock external MCP call logging
    external_action_id = uuid.uuid4()
    print(f"   Tool Name: {tool_name}")
    print(f"   Tool Arguments: {tool_arguments}")
    print(f"   Target MCP: {target_mcp}")
    print(f"   Created ActionHistory record: {external_action_id}")
    print(f"   Action Type: ExternalMCPCall")
    print(f"   Actor: PMCP_ActionServer")
    print(f"   Parent ID: {mock_action_id}")
    print(f"   Status: PENDING")
    print()
    
    # Simulate error scenario
    print("5. ERROR HANDLING (ErrorHandlingMiddleware)")
    
    # Show how error handler accesses context
    request_details = getattr(context, "pmcp_request_details", None)
    session_id_from_context = getattr(context, "pmcp_session_id", None)
    agent_identity_from_context = getattr(context, "pmcp_agent_identity", None)
    action_record = getattr(context, "pmcp_action_record", None)
    
    print("   Error handler has access to:")
    print(f"   - Request Details: {bool(request_details)}")
    print(f"   - Session ID: {session_id_from_context}")
    print(f"   - Agent Identity: {agent_identity_from_context}")
    print(f"   - Action Record: {action_record}")
    print()
    
    # Show final ActionHistory structure
    print("6. FINAL ACTIONHISTORY STRUCTURE")
    print("   Client Request:")
    print(f"     ID: {mock_action_id}")
    print(f"     Parent ID: null")
    print(f"     Session ID: {session_id}")
    print(f"     Agent Identity: {agent_identity}")
    print(f"     Actor: Client")
    print(f"     Action Type: ClientRequest")
    print(f"     Status: COMPLETED")
    print()
    print("   External MCP Call:")
    print(f"     ID: {external_action_id}")
    print(f"     Parent ID: {mock_action_id}")
    print(f"     Session ID: {session_id}")
    print(f"     Agent Identity: {agent_identity}")
    print(f"     Actor: PMCP_ActionServer")
    print(f"     Action Type: ExternalMCPCall")
    print(f"     Target MCP: {target_mcp}")
    print(f"     Status: COMPLETED")
    print()
    
    print("=== Context Flow Complete ===")


if __name__ == "__main__":
    demonstrate_context_flow()
