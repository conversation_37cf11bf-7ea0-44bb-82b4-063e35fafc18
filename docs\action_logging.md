# PMCP Action Logging System

This document describes the comprehensive action logging system implemented in the Provably Model Context Protocol (PMCP) Action Server.

## Overview

The PMCP Action Server logs **every** interaction to ensure complete auditability and traceability. This includes:

1. **Client Requests** - All incoming requests from AI agents
2. **External MCP Calls** - All outgoing calls to external MCP servers
3. **Internal Operations** - Key internal processing steps

All actions are logged to the `action_history` table in PostgreSQL with full request/response payloads and metadata.

## Architecture

### Components

1. **ActionLoggerService** (`action_server/services/action_logger.py`)
   - Core service for logging actions to the database
   - Provides specialized methods for different action types

2. **ComprehensiveLoggingMiddleware** (`action_server/middlewares/comprehensive_logging.py`)
   - FastMCP middleware that intercepts all MCP messages
   - Implements all available FastMCP hooks for complete coverage

3. **ExternalMCPClient** (`action_server/services/external_mcp_client.py`)
   - Client for making calls to external MCP servers
   - Automatically logs all outgoing calls

4. **ActionHistory Model** (`shared/models/action_history.py`)
   - Database model for storing action logs
   - Supports hierarchical relationships via `parent_id`

### Database Schema

The `action_history` table includes:

- `id` - Unique action identifier (UUID)
- `parent_id` - Links to parent action for request chains
- `session_id` - Groups actions within an agent session
- `timestamp` - When the action occurred
- `agent_identity` - Agent identity from ACK
- `actor` - Who performed the action (`Client`, `PMCP_ActionServer`)
- `action_type` - Type of action (`ClientRequest`, `ExternalMCPCall`, etc.)
- `target_mcp` - Which external MCP was targeted (if applicable)
- `request_payload` - Full request data (JSONB)
- `response_payload` - Full response data (JSONB)
- `status` - Action status (`in_progress`, `completed`, `failed`)
- `error_message` - Error details if action failed
- `is_provable` - Whether this action can be cryptographically proven
- `proof_bytes_reference` - Reference to proof data
- `ttl_expiry` - When this log entry expires

## Action Types

### ClientRequest
- **Actor**: `Client`
- **Description**: Incoming requests from AI agents
- **Logged by**: `ComprehensiveLoggingMiddleware.on_message()`
- **Provable**: Yes

### ExternalMCPCall
- **Actor**: `PMCP_ActionServer`
- **Description**: Outgoing calls to external MCP servers
- **Logged by**: `ExternalMCPClient` methods
- **Provable**: Yes

### PMCPInternalLogic
- **Actor**: `PMCP_ActionServer`
- **Description**: Internal processing operations
- **Logged by**: Various middleware hooks
- **Provable**: No (by default)

## FastMCP Middleware Hooks

The `ComprehensiveLoggingMiddleware` implements all available FastMCP hooks:

### Primary Hooks
- `on_message()` - **Main logging point** for all client requests
- `on_request()` - Additional processing for requests
- `on_notification()` - Additional processing for notifications

### Operation-Specific Hooks
- `on_call_tool()` - Tool execution logging
- `on_read_resource()` - Resource access logging
- `on_get_prompt()` - Prompt retrieval logging

### List Operation Hooks
- `on_list_tools()` - Tool listing (selective logging)
- `on_list_resources()` - Resource listing (selective logging)
- `on_list_resource_templates()` - Template listing (selective logging)
- `on_list_prompts()` - Prompt listing (selective logging)

**Note**: List operations are only logged when they're direct client requests to avoid log spam from internal operations.

## External MCP Integration

### Registering External MCPs

External MCPs can be registered in several ways:

1. **Default Configuration** (in `MCPRegistryService`)
2. **Runtime Registration** (via `mcp_registry.register_mcp()`)
3. **Configuration File** (see `config/external_mcps.example.json`)

### Available External MCP Operations

The system provides tools for interacting with external MCPs:

- `call_external_tool` - Call a tool on an external MCP
- `list_external_tools` - List available tools
- `list_external_resources` - List available resources
- `read_external_resource` - Read a resource
- `list_registered_mcps` - Show registered external MCPs

### Automatic Logging

All external MCP calls are automatically logged with:
- Full request/response payloads
- Target MCP identification
- Parent-child relationships for request chains
- Error handling and status tracking

## Session and Identity Management

### Session ID
- Groups related actions within an agent's workflow
- Currently generated per request (TODO: implement proper session management)
- Used for tracing complete interaction chains

### Agent Identity
- Extracted from request context
- Currently uses placeholder logic (TODO: implement ACK integration)
- Critical for audit trails and access control

## Request Chaining

Actions can be linked via `parent_id` to create audit trails:

```
ClientRequest (parent_id: null)
├── ExternalMCPCall (parent_id: client_request_id)
├── PMCPInternalLogic (parent_id: client_request_id)
└── ExternalMCPCall (parent_id: client_request_id)
```

## Usage Examples

### Logging a Client Request
```python
action_record = await ActionLoggerService.log_client_request(
    session_id=session_id,
    agent_identity="agent_123",
    method="tools/call",
    request_payload={"tool_name": "send_hello", "arguments": {"name": "World"}},
    response_payload={"result": "Hello World!"},
)
```

### Logging an External MCP Call
```python
action_record = await ActionLoggerService.log_external_mcp_call(
    session_id=session_id,
    agent_identity="agent_123",
    target_mcp="marketplace_mcp",
    method="tools/call",
    request_payload={"tool_name": "create_order", "arguments": {...}},
    parent_id=parent_action_id,
)
```

### Making External MCP Calls
```python
client = external_mcp_manager.get_client("marketplace_mcp")
result = await client.call_tool(
    tool_name="create_order",
    arguments={"item": "widget", "quantity": 5},
    session_id=session_id,
    agent_identity="agent_123",
    parent_id=parent_action_id,
)
```

## Configuration

### Environment Variables
- Standard database configuration (see `.env.example`)
- No additional environment variables required for logging

### Middleware Registration
```python
action_mcp.add_middleware(ComprehensiveLoggingMiddleware())
```

### External MCP Registration
```python
# At startup
await mcp_registry.initialize_default_mcps()

# Runtime registration
mcp_registry.register_mcp("new_mcp", "http://new-mcp:8004/mcp")
```

## Monitoring and Debugging

### Log Levels
- **INFO**: Successful operations, request completion
- **DEBUG**: Detailed action logging with IDs
- **WARNING**: Non-critical issues (e.g., missing action records)
- **ERROR**: Failed operations, database errors

### Key Log Fields
- `request_id` - Unique per request for correlation
- `session_id` - Groups related actions
- `action_id` - Database record ID
- `agent_identity` - Agent performing the action

### Database Queries

Find all actions for a session:
```sql
SELECT * FROM action_history WHERE session_id = 'session_uuid' ORDER BY timestamp;
```

Find request chains:
```sql
WITH RECURSIVE action_tree AS (
  SELECT * FROM action_history WHERE parent_id IS NULL AND session_id = 'session_uuid'
  UNION ALL
  SELECT ah.* FROM action_history ah
  JOIN action_tree at ON ah.parent_id = at.id
)
SELECT * FROM action_tree ORDER BY timestamp;
```

## Future Enhancements

### Planned Features
1. **ACK Integration** - Proper agent identity extraction
2. **Session Management** - Persistent session tracking
3. **Proof Generation** - Cryptographic proof creation
4. **Log Retention** - TTL-based log cleanup
5. **Performance Optimization** - Batch logging, async improvements
6. **Configuration UI** - Web interface for MCP management

### Security Considerations
1. **Payload Sanitization** - Remove sensitive data from logs
2. **Access Control** - Restrict log access by agent identity
3. **Encryption** - Encrypt sensitive payload data
4. **Audit Integrity** - Tamper-proof log storage

## Troubleshooting

### Common Issues

1. **Missing Action Records**
   - Check database connectivity
   - Verify middleware registration order
   - Review error logs for database transaction failures

2. **External MCP Connection Failures**
   - Verify MCP URLs and network connectivity
   - Check external MCP server status
   - Review external MCP client error logs

3. **Performance Issues**
   - Monitor database connection pool
   - Consider payload size limits
   - Review log retention policies

### Debug Mode
Enable debug logging to see detailed action logging:
```bash
export LOG_LEVEL=DEBUG
```

This will show all action creation and update operations with full context.
