import uuid
from typing import Any, Dict, List

from fastmcp import Context
from fastmcp.tools import Tool

from action_server.services.external_mcp_client import external_mcp_manager


async def call_external_tool(
    mcp_name: str,
    tool_name: str,
    arguments: Dict[str, Any],
    ctx: Context,
) -> Dict[str, Any]:
    """
    Call a tool on an external MCP server.
    
    Args:
        mcp_name: Name of the external MCP server
        tool_name: Name of the tool to call
        arguments: Arguments to pass to the tool
        ctx: FastMCP context
        
    Returns:
        The tool response from the external MCP
    """
    # Extract session information from context
    session_id = getattr(ctx, 'pmcp_session_id', uuid.uuid4())
    agent_identity = getattr(ctx, 'pmcp_agent_identity', 'unknown_agent')
    parent_id = getattr(ctx, 'pmcp_parent_action_id', None)
    
    # Get the external MCP client
    try:
        client = external_mcp_manager.get_client(mcp_name)
    except ValueError as e:
        return {
            "error": str(e),
            "available_mcps": external_mcp_manager.list_registered_mcps(),
        }
    
    # Call the external tool
    try:
        result = await client.call_tool(
            tool_name=tool_name,
            arguments=arguments,
            session_id=session_id,
            agent_identity=agent_identity,
            parent_id=parent_id,
        )
        return result
    except Exception as e:
        return {
            "error": f"Failed to call tool {tool_name} on {mcp_name}: {str(e)}",
            "mcp_name": mcp_name,
            "tool_name": tool_name,
        }


async def list_external_tools(
    mcp_name: str,
    ctx: Context,
) -> Dict[str, Any]:
    """
    List available tools on an external MCP server.
    
    Args:
        mcp_name: Name of the external MCP server
        ctx: FastMCP context
        
    Returns:
        List of available tools
    """
    # Extract session information from context
    session_id = getattr(ctx, 'pmcp_session_id', uuid.uuid4())
    agent_identity = getattr(ctx, 'pmcp_agent_identity', 'unknown_agent')
    parent_id = getattr(ctx, 'pmcp_parent_action_id', None)
    
    # Get the external MCP client
    try:
        client = external_mcp_manager.get_client(mcp_name)
    except ValueError as e:
        return {
            "error": str(e),
            "available_mcps": external_mcp_manager.list_registered_mcps(),
        }
    
    # List the external tools
    try:
        tools = await client.list_tools(
            session_id=session_id,
            agent_identity=agent_identity,
            parent_id=parent_id,
        )
        return {
            "mcp_name": mcp_name,
            "tools": tools,
        }
    except Exception as e:
        return {
            "error": f"Failed to list tools on {mcp_name}: {str(e)}",
            "mcp_name": mcp_name,
        }


async def list_external_resources(
    mcp_name: str,
    ctx: Context,
) -> Dict[str, Any]:
    """
    List available resources on an external MCP server.
    
    Args:
        mcp_name: Name of the external MCP server
        ctx: FastMCP context
        
    Returns:
        List of available resources
    """
    # Extract session information from context
    session_id = getattr(ctx, 'pmcp_session_id', uuid.uuid4())
    agent_identity = getattr(ctx, 'pmcp_agent_identity', 'unknown_agent')
    parent_id = getattr(ctx, 'pmcp_parent_action_id', None)
    
    # Get the external MCP client
    try:
        client = external_mcp_manager.get_client(mcp_name)
    except ValueError as e:
        return {
            "error": str(e),
            "available_mcps": external_mcp_manager.list_registered_mcps(),
        }
    
    # List the external resources
    try:
        resources = await client.list_resources(
            session_id=session_id,
            agent_identity=agent_identity,
            parent_id=parent_id,
        )
        return {
            "mcp_name": mcp_name,
            "resources": resources,
        }
    except Exception as e:
        return {
            "error": f"Failed to list resources on {mcp_name}: {str(e)}",
            "mcp_name": mcp_name,
        }


async def read_external_resource(
    mcp_name: str,
    resource_uri: str,
    ctx: Context,
) -> Dict[str, Any]:
    """
    Read a resource from an external MCP server.
    
    Args:
        mcp_name: Name of the external MCP server
        resource_uri: URI of the resource to read
        ctx: FastMCP context
        
    Returns:
        The resource content
    """
    # Extract session information from context
    session_id = getattr(ctx, 'pmcp_session_id', uuid.uuid4())
    agent_identity = getattr(ctx, 'pmcp_agent_identity', 'unknown_agent')
    parent_id = getattr(ctx, 'pmcp_parent_action_id', None)
    
    # Get the external MCP client
    try:
        client = external_mcp_manager.get_client(mcp_name)
    except ValueError as e:
        return {
            "error": str(e),
            "available_mcps": external_mcp_manager.list_registered_mcps(),
        }
    
    # Read the external resource
    try:
        resource = await client.read_resource(
            uri=resource_uri,
            session_id=session_id,
            agent_identity=agent_identity,
            parent_id=parent_id,
        )
        return {
            "mcp_name": mcp_name,
            "resource_uri": resource_uri,
            "resource": resource,
        }
    except Exception as e:
        return {
            "error": f"Failed to read resource {resource_uri} from {mcp_name}: {str(e)}",
            "mcp_name": mcp_name,
            "resource_uri": resource_uri,
        }


async def list_registered_mcps(ctx: Context) -> Dict[str, Any]:
    """
    List all registered external MCP servers.
    
    Args:
        ctx: FastMCP context
        
    Returns:
        List of registered MCP names
    """
    return {
        "registered_mcps": external_mcp_manager.list_registered_mcps(),
    }


# Create FastMCP tools
call_external_tool_tool = Tool.from_function(
    fn=call_external_tool,
    name="call_external_tool",
    description="Call a tool on an external MCP server",
    enabled=True,
)

list_external_tools_tool = Tool.from_function(
    fn=list_external_tools,
    name="list_external_tools",
    description="List available tools on an external MCP server",
    enabled=True,
)

list_external_resources_tool = Tool.from_function(
    fn=list_external_resources,
    name="list_external_resources",
    description="List available resources on an external MCP server",
    enabled=True,
)

read_external_resource_tool = Tool.from_function(
    fn=read_external_resource,
    name="read_external_resource",
    description="Read a resource from an external MCP server",
    enabled=True,
)

list_registered_mcps_tool = Tool.from_function(
    fn=list_registered_mcps,
    name="list_registered_mcps",
    description="List all registered external MCP servers",
    enabled=True,
)
