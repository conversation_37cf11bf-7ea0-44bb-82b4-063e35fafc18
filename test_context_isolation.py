#!/usr/bin/env python3
"""
Test script to verify that context variables are properly isolated per request.
"""

import asyncio
import uuid
import time
from datetime import datetime
from unittest.mock import Mock

from action_server.middlewares.request_logging import (
    RequestLoggingMiddleware,
    RequestContextData,
    RequestDetails,
    get_current_request_context,
    request_context_data
)


class MockMiddlewareContext:
    """Mock MiddlewareContext for testing."""
    
    def __init__(self, agent_name: str, tool_name: str):
        self.method = "call_tool"
        self.source = agent_name
        self.type = "request"
        self.timestamp = datetime.now()
        self.message = Mock()
        self.message.name = tool_name
        self.message.arguments = {"param": "value"}


async def simulate_request_processing(agent_name: str, tool_name: str, delay: float):
    """Simulate processing a request with some delay."""
    
    print(f"🚀 [{agent_name}] Starting request for tool '{tool_name}'")
    
    # Create middleware and context
    middleware = RequestLoggingMiddleware()
    context = MockMiddlewareContext(agent_name, tool_name)
    
    async def mock_call_next(ctx):
        # Get context data during request processing
        context_data = get_current_request_context()
        
        if context_data is None:
            print(f"❌ [{agent_name}] ERROR: No context data available!")
            return None
        
        print(f"📋 [{agent_name}] Context data found:")
        print(f"   Request ID: {str(context_data.request_id)[:8]}...")
        print(f"   Session ID: {str(context_data.session_id)[:8]}...")
        print(f"   Agent: {context_data.agent_identity}")
        print(f"   Tool: {context_data.tool_name}")
        
        # Simulate some processing time
        print(f"⏳ [{agent_name}] Processing for {delay}s...")
        await asyncio.sleep(delay)
        
        # Check context data is still available after delay
        context_data_after = get_current_request_context()
        if context_data_after and context_data_after.request_id == context_data.request_id:
            print(f"✅ [{agent_name}] Context data persisted through processing")
        else:
            print(f"❌ [{agent_name}] Context data lost during processing!")
        
        return {"result": f"success for {agent_name}"}
    
    # Process the request
    result = await middleware.on_message(context, mock_call_next)
    print(f"🏁 [{agent_name}] Request completed: {result}")
    
    return result


async def test_context_isolation():
    """Test that multiple concurrent requests have isolated context data."""
    
    print("=== Testing Context Variable Isolation ===\n")
    
    # Create multiple concurrent requests with different processing times
    tasks = [
        simulate_request_processing("Agent_A", "get_quote", 1.0),
        simulate_request_processing("Agent_B", "place_order", 0.5),
        simulate_request_processing("Agent_C", "check_balance", 1.5),
    ]
    
    print("🔄 Starting 3 concurrent requests...\n")
    
    # Run all requests concurrently
    results = await asyncio.gather(*tasks)
    
    print(f"\n📊 All requests completed:")
    for i, result in enumerate(results):
        print(f"   Task {i+1}: {result}")
    
    return True


async def test_context_cleanup():
    """Test that context data is cleaned up after request completion."""
    
    print("\n=== Testing Context Cleanup ===\n")
    
    # Check that no context data exists initially
    initial_context = get_current_request_context()
    print(f"Initial context (should be None): {initial_context}")
    
    # Process a request
    await simulate_request_processing("TestAgent", "test_tool", 0.1)
    
    # Check that context data is cleaned up after request
    final_context = get_current_request_context()
    print(f"Final context (should be None): {final_context}")
    
    if initial_context is None and final_context is None:
        print("✅ Context properly cleaned up")
        return True
    else:
        print("❌ Context not properly cleaned up")
        return False


async def test_manual_context_verification():
    """Test manual context setting and verification."""
    
    print("\n=== Testing Manual Context Verification ===\n")
    
    # Create test context data
    test_context = RequestContextData(
        request_id=uuid.uuid4(),
        session_id=uuid.uuid4(),
        agent_identity="manual_test_agent",
        timestamp=datetime.now().isoformat(),
        tool_name="manual_tool",
        tool_arguments={"test": "value"},
        request_details=RequestDetails(
            method="manual_test",
            source="manual_source",
            message_type="test",
            message_data={"test": "data"}
        )
    )
    
    print(f"Setting context with Request ID: {str(test_context.request_id)[:8]}...")
    
    # Set context data
    request_context_data.set(test_context)
    
    # Retrieve it
    retrieved_context = get_current_request_context()
    
    if retrieved_context and retrieved_context.request_id == test_context.request_id:
        print("✅ Manual context setting and retrieval works!")
        print(f"   Retrieved Request ID: {str(retrieved_context.request_id)[:8]}...")
        print(f"   Agent Identity: {retrieved_context.agent_identity}")
        return True
    else:
        print("❌ Manual context setting failed!")
        return False


async def main():
    """Run all tests."""
    
    print("Testing Context Variable Isolation and Lifecycle\n")
    
    test1_passed = await test_context_isolation()
    test2_passed = await test_context_cleanup()
    test3_passed = await test_manual_context_verification()
    
    print(f"\n=== Test Results ===")
    print(f"Context Isolation: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"Context Cleanup: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    print(f"Manual Context: {'✅ PASSED' if test3_passed else '❌ FAILED'}")
    
    if all([test1_passed, test2_passed, test3_passed]):
        print("\n🎉 All tests passed! Context variables work correctly.")
        print("\n📝 Key Findings:")
        print("   • Each request gets its own isolated context")
        print("   • Context persists throughout request processing")
        print("   • Context is automatically cleaned up after request")
        print("   • Multiple concurrent requests don't interfere")
    else:
        print("\n💥 Some tests failed. Check the implementation.")


if __name__ == "__main__":
    asyncio.run(main())
