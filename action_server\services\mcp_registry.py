from typing import Dict, List

from loguru import logger

from action_server.services.external_mcp_client import external_mcp_manager


class MCPRegistryService:
    """Service for managing external MCP server registrations."""

    def __init__(self):
        self._default_mcps: Dict[str, str] = {
            # Add default external MCPs here
            # Example: "example_mcp": "http://localhost:8001/mcp",
        }

    async def initialize_default_mcps(self) -> None:
        """Initialize default external MCP connections."""
        logger.info("Initializing default external MCP connections...")
        
        for mcp_name, mcp_url in self._default_mcps.items():
            try:
                external_mcp_manager.register_mcp(mcp_name, mcp_url)
                logger.info(f"Successfully registered external MCP: {mcp_name}")
            except Exception as e:
                logger.error(f"Failed to register external MCP {mcp_name}: {e}")

    def register_mcp(self, mcp_name: str, mcp_url: str) -> None:
        """Register a new external MCP server."""
        external_mcp_manager.register_mcp(mcp_name, mcp_url)
        logger.info(f"Registered external MCP: {mcp_name} at {mcp_url}")

    def list_registered_mcps(self) -> List[str]:
        """List all registered external MCP servers."""
        return external_mcp_manager.list_registered_mcps()

    def add_default_mcp(self, mcp_name: str, mcp_url: str) -> None:
        """Add a new default MCP configuration."""
        self._default_mcps[mcp_name] = mcp_url
        logger.info(f"Added default MCP configuration: {mcp_name}")


# Global instance
mcp_registry = MCPRegistryService()
