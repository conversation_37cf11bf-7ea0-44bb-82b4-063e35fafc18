import enum

class ActionType(str, enum.Enum):
    """Enum for action types."""

    CLIENT_REQUEST = "ClientRequest"
    EXTERNAL_MCP_CALL = "ExternalMCPCall"
    PMCP_INTERNAL_LOGIC = "PMCPInternalLogic"
    PROVER_REQUEST = "ProverRequest"

class Actor(str, enum.Enum):
    """Enum for actors."""

    CLIENT = "Client"
    PMCP_ACTION_SERVER = "PMCP_ActionServer"
    PROVER_SERVER = "ProverServer"

class RequestStatus(str, enum.Enum):
    """Enum for action statuses."""

    PENDING = "pending"
    COMPLETED = "completed"
    FAILED = "failed"
