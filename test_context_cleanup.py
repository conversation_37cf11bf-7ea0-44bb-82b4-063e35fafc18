#!/usr/bin/env python3
"""
Test script to verify that context variables are properly cleaned up between requests.
"""

import asyncio
import uuid
from datetime import datetime
from unittest.mock import Mock

from action_server.middlewares.request_logging import (
    RequestLoggingMiddleware,
    RequestContextData,
    RequestDetails,
    get_current_request_context,
    request_context_data,
    request_context_manager
)


class MockMiddlewareContext:
    """Mock MiddlewareContext for testing."""
    
    def __init__(self, agent_name: str, tool_name: str):
        self.method = "call_tool"
        self.source = agent_name
        self.type = "request"
        self.timestamp = datetime.now()
        self.message = Mock()
        self.message.name = tool_name
        self.message.arguments = {"param": "value"}


async def test_context_cleanup_with_manager():
    """Test that the context manager properly cleans up context data."""
    
    print("=== Testing Context Manager Cleanup ===\n")
    
    # Verify no initial context
    initial_context = get_current_request_context()
    print(f"1. Initial context: {initial_context}")
    assert initial_context is None, "Should start with no context"
    
    # Create test context data
    test_context = RequestContextData(
        request_id=uuid.uuid4(),
        session_id=uuid.uuid4(),
        agent_identity="test_agent_1",
        timestamp=datetime.now().isoformat(),
        tool_name="test_tool",
        tool_arguments={"test": "value"},
        request_details=RequestDetails(
            method="test",
            source="test_source",
            message_type="test",
            message_data={"test": "data"}
        )
    )
    
    print(f"2. Created test context with ID: {str(test_context.request_id)[:8]}...")
    
    # Use context manager
    async with request_context_manager(test_context) as ctx:
        # Verify context is available inside manager
        current_context = get_current_request_context()
        print(f"3. Context inside manager: {str(current_context.request_id)[:8]}..." if current_context else "None")
        assert current_context is not None, "Context should be available inside manager"
        assert current_context.request_id == test_context.request_id, "Context should match"
    
    # Verify context is cleaned up after manager
    final_context = get_current_request_context()
    print(f"4. Context after manager: {final_context}")
    assert final_context is None, "Context should be cleaned up after manager"
    
    print("✅ Context manager cleanup test passed!\n")
    return True


async def test_sequential_requests():
    """Test that sequential requests don't interfere with each other."""
    
    print("=== Testing Sequential Request Isolation ===\n")
    
    middleware = RequestLoggingMiddleware()
    
    async def mock_call_next(ctx):
        context_data = get_current_request_context()
        if context_data:
            print(f"   Processing request {str(context_data.request_id)[:8]}... from {context_data.agent_identity}")
            await asyncio.sleep(0.1)  # Simulate processing
            return {"result": "success"}
        return None
    
    # Process first request
    print("1. Processing first request...")
    context1 = MockMiddlewareContext("Agent_A", "tool_1")
    result1 = await middleware.on_message(context1, mock_call_next)
    
    # Check context is cleaned up
    context_after_1 = get_current_request_context()
    print(f"   Context after first request: {context_after_1}")
    
    # Process second request
    print("2. Processing second request...")
    context2 = MockMiddlewareContext("Agent_B", "tool_2")
    result2 = await middleware.on_message(context2, mock_call_next)
    
    # Check context is cleaned up
    context_after_2 = get_current_request_context()
    print(f"   Context after second request: {context_after_2}")
    
    # Verify both requests succeeded and context was cleaned up
    success = (
        result1 is not None and 
        result2 is not None and 
        context_after_1 is None and 
        context_after_2 is None
    )
    
    if success:
        print("✅ Sequential request isolation test passed!\n")
    else:
        print("❌ Sequential request isolation test failed!\n")
    
    return success


async def test_concurrent_requests():
    """Test that concurrent requests have isolated contexts."""
    
    print("=== Testing Concurrent Request Isolation ===\n")
    
    middleware = RequestLoggingMiddleware()
    request_contexts = {}
    
    async def mock_call_next_with_tracking(ctx, agent_name):
        context_data = get_current_request_context()
        if context_data:
            request_id = str(context_data.request_id)[:8]
            print(f"   [{agent_name}] Processing request {request_id}...")
            request_contexts[agent_name] = request_id
            await asyncio.sleep(0.2)  # Simulate processing
            
            # Verify context is still correct after delay
            context_after_delay = get_current_request_context()
            if context_after_delay and str(context_after_delay.request_id)[:8] == request_id:
                print(f"   [{agent_name}] Context preserved during processing")
                return {"result": "success", "agent": agent_name}
            else:
                print(f"   [{agent_name}] Context lost during processing!")
                return None
        return None
    
    # Create concurrent requests
    tasks = []
    for i, agent in enumerate(["Agent_A", "Agent_B", "Agent_C"]):
        context = MockMiddlewareContext(agent, f"tool_{i+1}")
        task = middleware.on_message(
            context, 
            lambda ctx, a=agent: mock_call_next_with_tracking(ctx, a)
        )
        tasks.append(task)
    
    print("Starting 3 concurrent requests...")
    results = await asyncio.gather(*tasks)
    
    # Check final context state
    final_context = get_current_request_context()
    print(f"Final context after all requests: {final_context}")
    
    # Verify all requests succeeded and had unique contexts
    success = (
        all(r is not None for r in results) and
        len(set(request_contexts.values())) == 3 and  # All unique request IDs
        final_context is None  # Context cleaned up
    )
    
    if success:
        print("✅ Concurrent request isolation test passed!")
        print(f"   Unique request IDs: {list(request_contexts.values())}")
    else:
        print("❌ Concurrent request isolation test failed!")
        print(f"   Results: {results}")
        print(f"   Request contexts: {request_contexts}")
    
    return success


async def main():
    """Run all tests."""
    
    print("Testing Context Variable Cleanup and Isolation\n")
    
    test1_passed = await test_context_cleanup_with_manager()
    test2_passed = await test_sequential_requests()
    test3_passed = await test_concurrent_requests()
    
    print(f"\n=== Test Results ===")
    print(f"Context Manager Cleanup: {'✅ PASSED' if test1_passed else '❌ FAILED'}")
    print(f"Sequential Request Isolation: {'✅ PASSED' if test2_passed else '❌ FAILED'}")
    print(f"Concurrent Request Isolation: {'✅ PASSED' if test3_passed else '❌ FAILED'}")
    
    if all([test1_passed, test2_passed, test3_passed]):
        print("\n🎉 All tests passed! Context cleanup is working correctly.")
    else:
        print("\n💥 Some tests failed. Context cleanup needs attention.")


if __name__ == "__main__":
    asyncio.run(main())
