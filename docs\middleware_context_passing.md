# PMCP Middleware Context Passing Guide

## Overview

This document explains how the PMCP Action Server's middleware system properly passes `request_details` and context information between hooks and to the error handler.

## Hook Execution Order

Based on FastMCP documentation, when a client calls a tool, multiple hooks are called in this order:

1. **`on_message`** - Called for ALL MCP messages (most general)
2. **`on_request`** - Called for requests that expect responses
3. **`on_call_tool`** - Called specifically for tool execution (most specific)

## Context Data Flow

### 1. Context Creation (`on_message`)

The `ComprehensiveLoggingMiddleware.on_message` hook is the first to execute and creates the shared context:

```python
def _get_or_create_context_data(self, context: MiddlewareContext) -> Dict[str, Any]:
    # Check if context data already exists
    if hasattr(context, "pmcp_context_data"):
        return context.pmcp_context_data
    
    # Create new context data
    context_data = {
        "request_id": str(uuid.uuid4()),
        "session_id": uuid.uuid4(),
        "agent_identity": context.source or "unknown",
        "method": context.method or "unknown",
        "start_time": time.perf_counter(),
        "request_details": {
            "method": method,
            "source": context.source,
            "message_type": context.type,
            "message_data": self._serialize_message(context.message),
            "timestamp": context.timestamp.isoformat(),
        }
    }
    
    # Store context data for use in other hooks
    setattr(context, "pmcp_context_data", context_data)
    setattr(context, "pmcp_session_id", session_id)
    setattr(context, "pmcp_agent_identity", agent_identity)
    setattr(context, "pmcp_request_details", context_data["request_details"])
    
    return context_data
```

### 2. Client Request Logging (`on_request`)

The `on_request` hook logs the incoming client request to the ActionHistory table:

```python
async def on_request(self, context: MiddlewareContext, call_next: CallNext):
    # Get context data (already exists from on_message)
    context_data = self._get_or_create_context_data(context)
    
    # Log the incoming client request to ActionHistory
    action_record = await ActionLoggerService.log_client_request(
        session_id=session_id,
        agent_identity=agent_identity,
        method=method,
        request_payload=request_details,
        status=RequestStatus.PENDING,
    )
    
    # Store additional context for other hooks
    setattr(context, "pmcp_parent_action_id", action_record.id)
    setattr(context, "pmcp_action_record", action_record)
```

### 3. Tool Execution Logging (`on_call_tool`)

The `on_call_tool` hook logs external MCP calls:

```python
async def on_call_tool(self, context: MiddlewareContext, call_next: CallNext):
    # Get context data from previous hooks
    context_data = getattr(context, "pmcp_context_data", None)
    parent_action_id = getattr(context, "pmcp_parent_action_id", None)
    
    # Log the external MCP call
    action_record = await ActionLoggerService.log_external_mcp_call(
        session_id=session_id,
        agent_identity=agent_identity,
        target_mcp=target_mcp,
        method=f"call_tool:{tool_name}",
        request_payload={"tool_name": tool_name, "arguments": tool_arguments},
        parent_id=parent_action_id,  # Links to the client request
        status=RequestStatus.PENDING,
    )
```

### 4. Error Handling

The `ErrorHandlingMiddleware` can access all context information:

```python
async def on_message(self, context: MiddlewareContext, call_next: CallNext):
    # Get request details from comprehensive logging middleware
    request_details = getattr(context, "pmcp_request_details", None)
    
    try:
        return await call_next(context)
    except Exception as e:
        # Include additional context if available
        session_id = getattr(context, "pmcp_session_id", None)
        agent_identity = getattr(context, "pmcp_agent_identity", None)
        action_record = getattr(context, "pmcp_action_record", None)
        
        error_log_details = {
            **request_details,
            "session_id": str(session_id) if session_id else None,
            "agent_identity": agent_identity,
            "action_id": str(action_record.id) if action_record else None,
        }
```

## Context Attributes Available

The following attributes are set on the `MiddlewareContext` and available to all subsequent hooks:

- `pmcp_context_data`: Complete context dictionary
- `pmcp_session_id`: UUID for the agent's workflow session
- `pmcp_agent_identity`: Agent identity from the request source
- `pmcp_request_details`: Serialized request information
- `pmcp_parent_action_id`: ID of the parent action (client request)
- `pmcp_action_record`: The ActionHistory record for the client request

## ActionHistory Logging Strategy

### Client Requests
- Logged in `on_request` hook
- Status starts as `PENDING`, updated to `COMPLETED` or `FAILED`
- Contains full request and response payloads

### External MCP Calls
- Logged in `on_call_tool` hook
- Linked to parent client request via `parent_id`
- Contains tool name, arguments, and target MCP information

### Hierarchy Example
```
Client Request (parent_id: null)
├── External MCP Call 1 (parent_id: client_request_id)
├── External MCP Call 2 (parent_id: client_request_id)
└── Internal Logic (parent_id: client_request_id)
```

## Middleware Registration Order

The middleware order in `__main__.py` is important:

```python
action_mcp.add_middleware(ComprehensiveLoggingMiddleware())  # First - creates context
action_mcp.add_middleware(ErrorHandlingMiddleware())        # Second - uses context
```

This ensures that `ComprehensiveLoggingMiddleware` runs first and creates the context that `ErrorHandlingMiddleware` can use.

## Benefits

1. **Consistent Context**: All hooks share the same session_id, request_id, and agent_identity
2. **Complete Audit Trail**: Every client request and external MCP call is logged to ActionHistory
3. **Error Context**: Error handlers have full context about what was happening when the error occurred
4. **Performance Tracking**: Timing information is consistent across all hooks
5. **Hierarchical Logging**: Parent-child relationships between actions are maintained
