import uuid
from datetime import datetime

from loguru import logger

from database.session import db_async_session_maker
from shared.models.action_history import ActionHistory
from shared.models.enums import ActionType, Actor, RequestStatus

class ActionLoggerService:
    """Service for logging all actions to the ActionHistory table."""

    @staticmethod
    async def log_action(
        session_id: uuid.UUID,
        agent_identity: str,
        actor: str,
        action_type: str,
        request_payload: dict | None = None,
        response_payload: dict | None = None,
        target_mcp: str | None = None,
        parent_id: uuid.UUID | None = None,
        is_provable: bool = False,
        status: str | None = None,
        error_message: str | None = None,
        proof_bytes_reference: str | None = None,
        ttl_expiry: datetime | None = None,
    ) -> ActionHistory:
        """
        Log an action to the ActionHistory table.

        Args:
            session_id: Unique ID for an agent's workflow
            agent_identity: Agent identity from ACK
            actor: Actor performing the action (e.g., 'Client', 'PMCP_ActionServer')
            action_type: Type of action (e.g., 'ClientRequest', 'ExternalMCPCall')
            request_payload: Request payload as JSON
            response_payload: Response payload as JSON
            target_mcp: Which MCP was targeted
            parent_id: FK to parent action (for chaining)
            is_provable: Whether this action is provable
            status: Status of the action
            error_message: Error message if any
            proof_bytes_reference: Reference to proof bytes
            ttl_expiry: TTL expiry timestamp

        Returns:
            The created ActionHistory record
        """
        async with db_async_session_maker() as session:
            try:
                action_record = ActionHistory(
                    session_id=session_id,
                    agent_identity=agent_identity,
                    actor=actor,
                    action_type=action_type,
                    request_payload=request_payload,
                    response_payload=response_payload,
                    target_mcp=target_mcp,
                    parent_id=parent_id,
                    is_provable=is_provable,
                    status=status,
                    error_message=error_message,
                    proof_bytes_reference=proof_bytes_reference,
                    ttl_expiry=ttl_expiry,
                )

                session.add(action_record)
                await session.commit()
                await session.refresh(action_record)

                logger.debug(
                    f"Logged action: {action_type} by {actor} for agent {agent_identity}",
                    action_id=str(action_record.id),
                    session_id=str(session_id),
                    target_mcp=target_mcp,
                )

                return action_record

            except Exception as e:
                await session.rollback()
                logger.error(
                    f"Failed to log action: {action_type} by {actor}",
                    error=str(e),
                    session_id=str(session_id),
                    agent_identity=agent_identity,
                )
                raise

    @staticmethod
    async def log_client_request(
        session_id: uuid.UUID,
        agent_identity: str,
        method: str,
        request_payload: dict,
        response_payload: dict | None = None,
        status: RequestStatus = RequestStatus.COMPLETED,
        error_message: str | None = None,
    ) -> ActionHistory:
        """Log an incoming client request."""
        return await ActionLoggerService.log_action(
            session_id=session_id,
            agent_identity=agent_identity,
            actor=Actor.CLIENT.value,
            action_type=ActionType.CLIENT_REQUEST.value,
            request_payload={
                "method": method,
                **request_payload,
            },
            response_payload=response_payload,
            status=status.value,
            error_message=error_message,
            is_provable=True,  # Client requests are provable
        )

    @staticmethod
    async def log_external_mcp_call(
        session_id: uuid.UUID,
        agent_identity: str,
        target_mcp: str,
        method: str,
        request_payload: dict,
        response_payload: dict | None = None,
        parent_id: uuid.UUID | None = None,
        status: RequestStatus = RequestStatus.COMPLETED,
        error_message: str | None = None,
    ) -> ActionHistory:
        """Log an outgoing call to an external MCP."""
        return await ActionLoggerService.log_action(
            session_id=session_id,
            agent_identity=agent_identity,
            actor=Actor.PMCP_ACTION_SERVER.value,
            action_type=ActionType.EXTERNAL_MCP_CALL.value,
            target_mcp=target_mcp,
            request_payload={
                "method": method,
                **request_payload,
            },
            response_payload=response_payload,
            parent_id=parent_id,
            status=status.value,
            error_message=error_message,
            is_provable=True,  # External MCP calls are provable
        )

    @staticmethod
    async def log_internal_logic(
        session_id: uuid.UUID,
        agent_identity: str,
        operation: str,
        details: dict | None = None,
        parent_id: uuid.UUID | None = None,
        status: RequestStatus = RequestStatus.COMPLETED,
        error_message: str | None = None,
    ) -> ActionHistory:
        """Log internal PMCP logic operations."""
        return await ActionLoggerService.log_action(
            session_id=session_id,
            agent_identity=agent_identity,
            actor=Actor.PMCP_ACTION_SERVER.value,
            action_type=ActionType.PMCP_INTERNAL_LOGIC.value,
            request_payload={
                "operation": operation,
                **(details or {}),
            },
            parent_id=parent_id,
            status=status.value,
            error_message=error_message,
            is_provable=False,  # Internal logic is not provable by default
        )

    @staticmethod
    async def update_action_response(
        action_id: uuid.UUID,
        response_payload: dict,
        status: RequestStatus = RequestStatus.COMPLETED,
        error_message: str | None = None,
    ) -> None:
        """Update an existing action record with response data."""
        async with db_async_session_maker() as session:
            try:
                action_record = await session.get(ActionHistory, action_id)
                if action_record:
                    action_record.response_payload = response_payload
                    action_record.status = status.value
                    action_record.error_message = error_message

                    await session.commit()

                    logger.debug(
                        f"Updated action response: {action_id}",
                        status=status.value,
                        has_error=error_message is not None,
                    )
                else:
                    logger.warning(f"Action record not found for update: {action_id}")

            except Exception as e:
                await session.rollback()
                logger.error(
                    f"Failed to update action response: {action_id}",
                    error=str(e),
                )
                raise