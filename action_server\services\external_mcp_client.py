import uuid
from typing import Any

from fastmcp import Client
from mcp.types import Tool, Resource, TextResourceContents, BlobResourceContents
from loguru import logger

from action_server.services.action_logger import ActionLoggerService


class ExternalMCPClient:
    """Client for making calls to external MCP servers with automatic logging."""

    def __init__(self, mcp_url: str, mcp_name: str):
        """
        Initialize the external MCP client.
        
        Args:
            mcp_url: URL of the external MCP server
            mcp_name: Name identifier for the external MCP
        """
        self.mcp_url = mcp_url
        self.mcp_name = mcp_name
        self._client = Client(mcp_url)

    async def call_tool(
        self,
        tool_name: str,
        arguments: dict[str, Any],
        session_id: uuid.UUID,
        agent_identity: str,
        parent_id: uuid.UUID | None = None,
    ) -> dict[str, Any]:
        """
        Call a tool on the external MCP server.
        
        Args:
            tool_name: Name of the tool to call
            arguments: Arguments to pass to the tool
            session_id: Session ID for logging
            agent_identity: Agent identity for logging
            parent_id: Parent action ID for chaining
            
        Returns:
            The tool response
        """
        request_payload = {
            "tool_name": tool_name,
            "arguments": arguments,
        }
        
        # Log the outgoing call
        action_record = await ActionLoggerService.log_external_mcp_call(
            session_id=session_id,
            agent_identity=agent_identity,
            target_mcp=self.mcp_name,
            method="tools/call",
            request_payload=request_payload,
            parent_id=parent_id,
            status="in_progress",
        )
        
        try:
            async with self._client:
                result = await self._client.call_tool(tool_name, arguments)
                
                # Update the action record with the response
                await ActionLoggerService.update_action_response(
                    action_id=action_record.id,
                    response_payload={"result": result},
                    status="completed",
                )
                
                logger.info(
                    f"Successfully called tool {tool_name} on {self.mcp_name}",
                    session_id=str(session_id),
                    action_id=str(action_record.id),
                )
                
                return {"result": result}
                
        except Exception as e:
            error_message = str(e)
            
            # Update the action record with the error
            await ActionLoggerService.update_action_response(
                action_id=action_record.id,
                response_payload={"error": error_message},
                status="failed",
                error_message=error_message,
            )
            
            logger.error(
                f"Failed to call tool {tool_name} on {self.mcp_name}",
                error=error_message,
                session_id=str(session_id),
                action_id=str(action_record.id),
            )
            
            raise

    async def list_tools(
        self,
        session_id: uuid.UUID,
        agent_identity: str,
        parent_id: uuid.UUID | None = None,
    ) -> list[Tool]:
        """List available tools on the external MCP server."""
        request_payload = {}
        
        # Log the outgoing call
        action_record = await ActionLoggerService.log_external_mcp_call(
            session_id=session_id,
            agent_identity=agent_identity,
            target_mcp=self.mcp_name,
            method="tools/list",
            request_payload=request_payload,
            parent_id=parent_id,
            status="in_progress",
        )
        
        try:
            async with self._client:
                tools = await self._client.list_tools()
                
                # Update the action record with the response
                await ActionLoggerService.update_action_response(
                    action_id=action_record.id,
                    response_payload={"tools": tools},
                    status="completed",
                )
                
                logger.info(
                    f"Successfully listed tools on {self.mcp_name}",
                    session_id=str(session_id),
                    action_id=str(action_record.id),
                    tool_count=len(tools),
                )
                
                return tools
                
        except Exception as e:
            error_message = str(e)
            
            # Update the action record with the error
            await ActionLoggerService.update_action_response(
                action_id=action_record.id,
                response_payload={"error": error_message},
                status="failed",
                error_message=error_message,
            )
            
            logger.error(
                f"Failed to list tools on {self.mcp_name}",
                error=error_message,
                session_id=str(session_id),
                action_id=str(action_record.id),
            )
            
            raise

    async def list_resources(
        self,
        session_id: uuid.UUID,
        agent_identity: str,
        parent_id: uuid.UUID | None = None,
    ) -> list[Resource]:
        """List available resources on the external MCP server."""
        request_payload = {}
        
        # Log the outgoing call
        action_record = await ActionLoggerService.log_external_mcp_call(
            session_id=session_id,
            agent_identity=agent_identity,
            target_mcp=self.mcp_name,
            method="resources/list",
            request_payload=request_payload,
            parent_id=parent_id,
            status="in_progress",
        )
        
        try:
            async with self._client:
                resources = await self._client.list_resources()
                
                # Update the action record with the response
                await ActionLoggerService.update_action_response(
                    action_id=action_record.id,
                    response_payload={"resources": resources},
                    status="completed",
                )
                
                logger.info(
                    f"Successfully listed resources on {self.mcp_name}",
                    session_id=str(session_id),
                    action_id=str(action_record.id),
                    resource_count=len(resources),
                )
                
                return resources
                
        except Exception as e:
            error_message = str(e)
            
            # Update the action record with the error
            await ActionLoggerService.update_action_response(
                action_id=action_record.id,
                response_payload={"error": error_message},
                status="failed",
                error_message=error_message,
            )
            
            logger.error(
                f"Failed to list resources on {self.mcp_name}",
                error=error_message,
                session_id=str(session_id),
                action_id=str(action_record.id),
            )
            
            raise

    async def read_resource(
        self,
        uri: str,
        session_id: uuid.UUID,
        agent_identity: str,
        parent_id: uuid.UUID | None = None,
    ) -> list[TextResourceContents | BlobResourceContents]:
        """Read a resource from the external MCP server."""
        request_payload = {"uri": uri}
        
        # Log the outgoing call
        action_record = await ActionLoggerService.log_external_mcp_call(
            session_id=session_id,
            agent_identity=agent_identity,
            target_mcp=self.mcp_name,
            method="resources/read",
            request_payload=request_payload,
            parent_id=parent_id,
            status="in_progress",
        )
        
        try:
            async with self._client:
                resource = await self._client.read_resource(uri)
                
                # Update the action record with the response
                await ActionLoggerService.update_action_response(
                    action_id=action_record.id,
                    response_payload={"resource": resource},
                    status="completed",
                )
                
                logger.info(
                    f"Successfully read resource {uri} from {self.mcp_name}",
                    session_id=str(session_id),
                    action_id=str(action_record.id),
                )
                
                return resource
                
        except Exception as e:
            error_message = str(e)
            
            # Update the action record with the error
            await ActionLoggerService.update_action_response(
                action_id=action_record.id,
                response_payload={"error": error_message},
                status="failed",
                error_message=error_message,
            )
            
            logger.error(
                f"Failed to read resource {uri} from {self.mcp_name}",
                error=error_message,
                session_id=str(session_id),
                action_id=str(action_record.id),
            )
            
            raise


class ExternalMCPManager:
    """Manager for multiple external MCP clients."""

    def __init__(self):
        self._clients: dict[str, ExternalMCPClient] = {}

    def register_mcp(self, mcp_name: str, mcp_url: str) -> None:
        """Register an external MCP server."""
        self._clients[mcp_name] = ExternalMCPClient(mcp_url, mcp_name)
        logger.info(f"Registered external MCP: {mcp_name} at {mcp_url}")

    def get_client(self, mcp_name: str) -> ExternalMCPClient:
        """Get a client for the specified MCP."""
        if mcp_name not in self._clients:
            raise ValueError(f"MCP '{mcp_name}' is not registered")
        return self._clients[mcp_name]

    def list_registered_mcps(self) -> list[str]:
        """List all registered MCP names."""
        return list(self._clients.keys())


# Global instance for managing external MCPs
external_mcp_manager = ExternalMCPManager()
