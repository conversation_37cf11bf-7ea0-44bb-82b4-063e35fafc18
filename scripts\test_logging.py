#!/usr/bin/env python3
"""
Test script for PMCP action logging functionality.

This script demonstrates the comprehensive logging system by:
1. Making client requests to the PMCP Action Server
2. Showing how external MCP calls are logged
3. Querying the action_history table to verify logging

Usage:
    uv run python scripts/test_logging.py
"""

import asyncio
import uuid
from datetime import datetime, timezone

from fastmcp import Client
from loguru import logger
from sqlalchemy import select

from action_server.core.config import Config
from action_server.services.action_logger import ActionLoggerService
from action_server.services.external_mcp_client import external_mcp_manager
from database.session import db_async_session_maker
from shared.models.action_history import ActionHistory


async def test_direct_logging():
    """Test direct logging to ActionHistory table."""
    logger.info("Testing direct action logging...")
    
    session_id = uuid.uuid4()
    agent_identity = "test_agent_123"
    
    # Test client request logging
    client_action = await ActionLoggerService.log_client_request(
        session_id=session_id,
        agent_identity=agent_identity,
        method="tools/call",
        request_payload={
            "tool_name": "send_hello",
            "arguments": {"name": "Test User"}
        },
        response_payload={
            "result": "Hello Test User!"
        },
    )
    
    logger.info(f"Logged client request: {client_action.id}")
    
    # Test external MCP call logging
    external_action = await ActionLoggerService.log_external_mcp_call(
        session_id=session_id,
        agent_identity=agent_identity,
        target_mcp="test_mcp",
        method="tools/call",
        request_payload={
            "tool_name": "test_tool",
            "arguments": {"param": "value"}
        },
        response_payload={
            "result": "External MCP response"
        },
        parent_id=client_action.id,
    )
    
    logger.info(f"Logged external MCP call: {external_action.id}")
    
    # Test internal logic logging
    internal_action = await ActionLoggerService.log_internal_logic(
        session_id=session_id,
        agent_identity=agent_identity,
        operation="test_operation",
        details={"step": "validation", "status": "passed"},
        parent_id=client_action.id,
    )
    
    logger.info(f"Logged internal logic: {internal_action.id}")
    
    return session_id


async def test_client_requests():
    """Test logging via actual client requests to the Action Server."""
    logger.info("Testing client request logging via FastMCP client...")
    
    # Create a client to connect to the Action Server
    client = Client(f"http://localhost:{Config.MCP_PORT}/mcp")
    
    try:
        async with client:
            # Test basic tool call
            result = await client.call_tool("send_hello", {"name": "Logging Test"})
            logger.info(f"Tool call result: {result}")
            
            # Test listing tools
            tools = await client.list_tools()
            logger.info(f"Available tools: {len(tools)}")
            
            # Test listing registered MCPs
            mcps_result = await client.call_tool("list_registered_mcps", {})
            logger.info(f"Registered MCPs: {mcps_result}")
            
    except Exception as e:
        logger.error(f"Client request test failed: {e}")
        logger.info("Make sure the Action Server is running on the configured port")


async def query_action_history(session_id: uuid.UUID = None):
    """Query and display action history from the database."""
    logger.info("Querying action history...")
    
    async with db_async_session_maker() as session:
        # Build query
        query = select(ActionHistory).order_by(ActionHistory.timestamp.desc())
        
        if session_id:
            query = query.where(ActionHistory.session_id == session_id)
            logger.info(f"Filtering by session_id: {session_id}")
        else:
            # Limit to recent actions if no session filter
            query = query.limit(10)
            logger.info("Showing last 10 actions")
        
        result = await session.execute(query)
        actions = result.scalars().all()
        
        if not actions:
            logger.warning("No actions found in database")
            return
        
        logger.info(f"Found {len(actions)} action(s):")
        
        for action in actions:
            logger.info(
                f"  {action.timestamp.isoformat()} | "
                f"{action.actor} | "
                f"{action.action_type} | "
                f"Agent: {action.agent_identity} | "
                f"Status: {action.status} | "
                f"Target: {action.target_mcp or 'N/A'}"
            )
            
            if action.parent_id:
                logger.info(f"    └─ Parent: {action.parent_id}")
            
            if action.error_message:
                logger.info(f"    └─ Error: {action.error_message}")


async def test_external_mcp_registration():
    """Test external MCP registration and client creation."""
    logger.info("Testing external MCP registration...")
    
    # Register a test MCP (this will fail to connect, but will test the logging)
    test_mcp_url = "http://localhost:9999/mcp"  # Non-existent server
    external_mcp_manager.register_mcp("test_mcp", test_mcp_url)
    
    logger.info(f"Registered test MCP: test_mcp at {test_mcp_url}")
    
    # List registered MCPs
    registered = external_mcp_manager.list_registered_mcps()
    logger.info(f"Currently registered MCPs: {registered}")
    
    # Try to use the external MCP client (this will fail but should log the attempt)
    try:
        client = external_mcp_manager.get_client("test_mcp")
        session_id = uuid.uuid4()
        
        # This will fail but should create a log entry
        await client.call_tool(
            tool_name="test_tool",
            arguments={"test": "value"},
            session_id=session_id,
            agent_identity="test_agent",
        )
    except Exception as e:
        logger.info(f"Expected failure when calling non-existent MCP: {e}")


async def main():
    """Run all logging tests."""
    logger.info("Starting PMCP action logging tests...")
    logger.info("=" * 60)
    
    try:
        # Test 1: Direct logging
        logger.info("Test 1: Direct Action Logging")
        logger.info("-" * 30)
        test_session_id = await test_direct_logging()
        
        logger.info("\n")
        
        # Test 2: External MCP registration
        logger.info("Test 2: External MCP Registration")
        logger.info("-" * 30)
        await test_external_mcp_registration()
        
        logger.info("\n")
        
        # Test 3: Client requests (optional - requires running server)
        logger.info("Test 3: Client Request Logging")
        logger.info("-" * 30)
        await test_client_requests()
        
        logger.info("\n")
        
        # Test 4: Query action history
        logger.info("Test 4: Action History Query")
        logger.info("-" * 30)
        await query_action_history(test_session_id)
        
        logger.info("\n")
        logger.info("All tests completed!")
        
    except Exception as e:
        logger.error(f"Test failed: {e}")
        raise


if __name__ == "__main__":
    asyncio.run(main())
